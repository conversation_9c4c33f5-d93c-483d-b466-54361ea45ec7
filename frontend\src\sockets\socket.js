import { io } from "socket.io-client";

// Use the production URL for deployed version, localhost for development
const SOCKET_URL = import.meta.env.PROD 
  ? "https://trello-7fyi-git-main-tayyabs-projects-9d235f55.vercel.app"
  : "http://localhost:4000";

const socket = io(SOCKET_URL, { 
  withCredentials: true,
  transports: ['websocket', 'polling']
});

// Enhanced board joining with authentication
export const authenticateUser = (userId) => {
  socket.emit("authenticate", userId);
};

export const joinBoards = (boards) => {
  boards.forEach((board) => {
    socket.emit("joinBoard", board._id);
  });
};

export const leaveBoard = (boardId) => {
  socket.emit("leaveBoard", boardId);
};

// Typing indicators for real-time collaboration
export const sendTypingIndicator = (boardId, userName, isTyping) => {
  socket.emit("typing", { boardId, userName, isTyping });
};

// Enhanced notification system
export const sendNotification = (boardId, message, type = "general") => {
  socket.emit("notify", { boardId, message, type });
};

// Connection status helpers
export const isConnected = () => socket.connected;

export const onConnect = (callback) => {
  socket.on("connect", callback);
};

export const onDisconnect = (callback) => {
  socket.on("disconnect", callback);
};

export default socket;
